package features

import "fmt"

// FeatureType represents the type of a feature
type FeatureType string

const (
	FeatureTypeNumeric  FeatureType = "numeric"
	FeatureTypeNominal  FeatureType = "nominal"
	FeatureTypeBinary   FeatureType = "binary"
	FeatureTypeDate     FeatureType = "date"
	FeatureTypeDateTime FeatureType = "datetime"
	FeatureTypeTime     FeatureType = "time"
)

// HandleAsType represents how to handle the feature internally
type HandleAsType string

const (
	HandleAsInteger HandleAsType = "integer"
	HandleAsFloat   HandleAsType = "float"
	HandleAsString  HandleAsType = "string"
)

// FeatureInfo contains metadata about a feature from YAML
// This saves only the name, type, and distribution information from training data
type FeatureInfo struct {
	Name     string       `yaml:"-"`                // Set from map key
	Type     FeatureType  `yaml:"type"`             // nominal/numeric/binary/date/datetime/time
	HandleAs HandleAsType `yaml:"handle_as"`        // integer/float/string
	Values   []string     `yaml:"values,omitempty"` // For nominal/binary types
	Min      *float64     `yaml:"min,omitempty"`    // For numeric validation
	Max      *float64     `yaml:"max,omitempty"`    // For numeric validation
}

// IsNumeric returns true if the feature should be handled as a numeric type
func (fi *FeatureInfo) IsNumeric() bool {
	return fi.Type == FeatureTypeNumeric
}

// IsNominal returns true if the feature should be handled as a nominal/categorical type
func (fi *FeatureInfo) IsNominal() bool {
	return fi.Type == FeatureTypeNominal
}

// IsBinary returns true if the feature should be handled as a binary type
func (fi *FeatureInfo) IsBinary() bool {
	return fi.Type == FeatureTypeBinary
}

// ShouldHandleAsInteger returns true if the feature should be converted to integer
func (fi *FeatureInfo) ShouldHandleAsInteger() bool {
	return fi.HandleAs == HandleAsInteger
}

// ShouldHandleAsFloat returns true if the feature should be converted to float
func (fi *FeatureInfo) ShouldHandleAsFloat() bool {
	return fi.HandleAs == HandleAsFloat
}

// ShouldHandleAsString returns true if the feature should be converted to string
func (fi *FeatureInfo) ShouldHandleAsString() bool {
	return fi.HandleAs == HandleAsString
}

// Validate checks if the feature info is valid
func (fi *FeatureInfo) Validate() error {
	if fi.Name == "" {
		return fmt.Errorf("feature name cannot be empty")
	}

	// Validate type
	switch fi.Type {
	case FeatureTypeNumeric, FeatureTypeNominal, FeatureTypeBinary, 
		 FeatureTypeDate, FeatureTypeDateTime, FeatureTypeTime:
		// Valid types
	default:
		return fmt.Errorf("invalid feature type: %s", fi.Type)
	}

	// Validate handle_as
	switch fi.HandleAs {
	case HandleAsInteger, HandleAsFloat, HandleAsString:
		// Valid handle_as types
	default:
		return fmt.Errorf("invalid handle_as type: %s", fi.HandleAs)
	}

	// Validate that nominal/binary features have values if needed
	if (fi.Type == FeatureTypeNominal || fi.Type == FeatureTypeBinary) && len(fi.Values) == 0 {
		// This is okay - values can be inferred from data
	}

	// Validate numeric constraints
	if fi.Min != nil && fi.Max != nil && *fi.Min > *fi.Max {
		return fmt.Errorf("min value (%f) cannot be greater than max value (%f)", *fi.Min, *fi.Max)
	}

	return nil
}
