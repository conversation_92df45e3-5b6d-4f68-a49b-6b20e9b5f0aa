package dataset

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internal/io/formats/csv"
)

// ColumnType represents how to interpret a column during loading
type ColumnType string

const (
	ColumnTypeInteger ColumnType = "integer"
	ColumnTypeFloat   ColumnType = "float"
	ColumnTypeString  ColumnType = "string"
)

// ColumnSpec specifies how to load a specific column
type ColumnSpec struct {
	Name string
	Type ColumnType
}

// Loader handles loading CSV data into typed Dataset structures
type Loader struct {
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadFromCSV loads a CSV file into a Dataset using column specifications
func (l *Loader) LoadFromCSV(csvPath string, columnSpecs []ColumnSpec) (*Dataset, error) {
	// 1. Parse raw CSV data using format layer
	rawData, err := l.csvParser.ParseFile(csvPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV: %w", err)
	}

	// 2. Validate structure
	if err := l.validateStructure(rawData, columnSpecs); err != nil {
		return nil, fmt.Errorf("structure validation failed: %w", err)
	}

	// 3. Convert to typed dataset using efficient iterators
	dataset, err := l.convertToDataset(rawData, columnSpecs)
	if err != nil {
		return nil, fmt.Errorf("conversion failed: %w", err)
	}

	return dataset, nil
}

// LoadFromCSVAsStrings loads a CSV file with all columns as strings (no type conversion)
func (l *Loader) LoadFromCSVAsStrings(csvPath string) (*Dataset, error) {
	// Parse raw CSV data
	rawData, err := l.csvParser.ParseFile(csvPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV: %w", err)
	}

	// Create column specs for all columns as strings
	columnSpecs := make([]ColumnSpec, len(rawData.Headers))
	for i, header := range rawData.Headers {
		columnSpecs[i] = ColumnSpec{
			Name: header,
			Type: ColumnTypeString,
		}
	}

	return l.convertToDataset(rawData, columnSpecs)
}

// validateStructure ensures CSV structure matches column specifications
func (l *Loader) validateStructure(rawData *csv.ParsedData, columnSpecs []ColumnSpec) error {
	// Create a map of required columns
	requiredColumns := make(map[string]bool)
	for _, spec := range columnSpecs {
		requiredColumns[spec.Name] = true
	}

	// Check that all required columns are present
	for _, spec := range columnSpecs {
		found := false
		for _, header := range rawData.Headers {
			if header == spec.Name {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("required column '%s' not found in CSV headers", spec.Name)
		}
	}

	// Check for duplicate headers
	headerSet := make(map[string]bool)
	for _, header := range rawData.Headers {
		if headerSet[header] {
			return fmt.Errorf("duplicate header '%s' found", header)
		}
		headerSet[header] = true
	}

	return nil
}

// convertToDataset converts raw string data to typed columns using efficient iterators
func (l *Loader) convertToDataset(rawData *csv.ParsedData, columnSpecs []ColumnSpec) (*Dataset, error) {
	dataset := NewDataset()

	// Create a map for quick column spec lookup
	specMap := make(map[string]ColumnSpec)
	for _, spec := range columnSpecs {
		specMap[spec.Name] = spec
	}

	// Convert each specified column using efficient iterators
	for _, spec := range columnSpecs {
		// Get column iterator (no data copying)
		columnIter, err := rawData.GetColumnIteratorByName(spec.Name)
		if err != nil {
			return nil, fmt.Errorf("failed to get column iterator for %s: %w", spec.Name, err)
		}

		// Convert based on column type using the iterator
		switch spec.Type {
		case ColumnTypeInteger:
			intData, nullMask, err := l.convertToIntDataFromIterator(columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to integer: %w", spec.Name, err)
			}
			if err := dataset.AddIntColumn(spec.Name, intData, nullMask); err != nil {
				return nil, fmt.Errorf("failed to add int column %s: %w", spec.Name, err)
			}

		case ColumnTypeFloat:
			floatData, nullMask, err := l.convertToFloatDataFromIterator(columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to float: %w", spec.Name, err)
			}
			if err := dataset.AddFloatColumn(spec.Name, floatData, nullMask); err != nil {
				return nil, fmt.Errorf("failed to add float column %s: %w", spec.Name, err)
			}

		case ColumnTypeString:
			stringData, nullMask, err := l.convertToStringDataFromIterator(columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to string: %w", spec.Name, err)
			}
			if err := dataset.AddStringColumn(spec.Name, stringData, nullMask); err != nil {
				return nil, fmt.Errorf("failed to add string column %s: %w", spec.Name, err)
			}

		default:
			return nil, fmt.Errorf("unsupported column type: %s for column %s", spec.Type, spec.Name)
		}
	}

	return dataset, nil
}

// convertToIntDataFromIterator converts string data to integer arrays using iterator (no copying)
func (l *Loader) convertToIntDataFromIterator(iter *csv.ColumnIterator) ([]int64, []bool, error) {
	length := iter.Length()
	intData := make([]int64, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			continue
		}

		intVal, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			return nil, nil, fmt.Errorf("row %d: cannot convert '%s' to integer", i, value)
		}
		intData[i] = intVal
	}

	return intData, nullMask, nil
}

// convertToFloatDataFromIterator converts string data to float arrays using iterator (no copying)
func (l *Loader) convertToFloatDataFromIterator(iter *csv.ColumnIterator) ([]float64, []bool, error) {
	length := iter.Length()
	floatData := make([]float64, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			continue
		}

		floatVal, err := strconv.ParseFloat(value, 64)
		if err != nil {
			return nil, nil, fmt.Errorf("row %d: cannot convert '%s' to float", i, value)
		}
		floatData[i] = floatVal
	}

	return floatData, nullMask, nil
}

// convertToStringDataFromIterator converts string data to string arrays using iterator (no copying)
func (l *Loader) convertToStringDataFromIterator(iter *csv.ColumnIterator) ([]string, []bool, error) {
	length := iter.Length()
	stringData := make([]string, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			stringData[i] = ""
		} else {
			stringData[i] = value
		}
	}

	return stringData, nullMask, nil
}
