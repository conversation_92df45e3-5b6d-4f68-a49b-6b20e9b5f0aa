package dataset

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internal/io/formats/csv"
)

// FeatureType represents the type of a feature
type FeatureType string

const (
	FeatureTypeNumeric  FeatureType = "numeric"
	FeatureTypeNominal  FeatureType = "nominal"
	FeatureTypeBinary   FeatureType = "binary"
	FeatureTypeDate     FeatureType = "date"
	FeatureTypeDateTime FeatureType = "datetime"
	FeatureTypeTime     FeatureType = "time"
)

// HandleAsType represents how to handle the feature internally
type HandleAsType string

const (
	HandleAsInteger HandleAsType = "integer"
	HandleAsFloat   HandleAsType = "float"
	HandleAsString  HandleAsType = "string"
)

// FeatureInfo contains metadata about a feature from YAML
type FeatureInfo struct {
	Name     string       `yaml:"-"`                // Set from map key
	Type     FeatureType  `yaml:"type"`             // nominal/numeric/binary/date/datetime/time
	HandleAs HandleAsType `yaml:"handle_as"`        // integer/float/string
	Values   []string     `yaml:"values,omitempty"` // For nominal/binary types
	Min      *float64     `yaml:"min,omitempty"`    // For numeric validation
	Max      *float64     `yaml:"max,omitempty"`    // For numeric validation
}

// Column interface for different column types
type Column interface {
	GetValue(index int) (interface{}, error)
	GetStringValue(index int) (string, error)
	IsNull(index int) bool
	Length() int
	Type() string
}

// IntColumn stores integer data
type IntColumn struct {
	Data     []int64
	NullMask []bool
	Name     string
}

func (c *IntColumn) GetValue(index int) (interface{}, error) {
	if index < 0 || index >= len(c.Data) {
		return nil, fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return nil, nil
	}
	return c.Data[index], nil
}

func (c *IntColumn) GetStringValue(index int) (string, error) {
	if index < 0 || index >= len(c.Data) {
		return "", fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return "", nil
	}
	return strconv.FormatInt(c.Data[index], 10), nil
}

func (c *IntColumn) IsNull(index int) bool {
	return index >= 0 && index < len(c.NullMask) && c.NullMask[index]
}

func (c *IntColumn) Length() int {
	return len(c.Data)
}

func (c *IntColumn) Type() string {
	return "integer"
}

// FloatColumn stores float data
type FloatColumn struct {
	Data     []float64
	NullMask []bool
	Name     string
}

func (c *FloatColumn) GetValue(index int) (interface{}, error) {
	if index < 0 || index >= len(c.Data) {
		return nil, fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return nil, nil
	}
	return c.Data[index], nil
}

func (c *FloatColumn) GetStringValue(index int) (string, error) {
	if index < 0 || index >= len(c.Data) {
		return "", fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return "", nil
	}
	return strconv.FormatFloat(c.Data[index], 'f', -1, 64), nil
}

func (c *FloatColumn) IsNull(index int) bool {
	return index >= 0 && index < len(c.NullMask) && c.NullMask[index]
}

func (c *FloatColumn) Length() int {
	return len(c.Data)
}

func (c *FloatColumn) Type() string {
	return "float"
}

// StringColumn stores string data
type StringColumn struct {
	Data     []string
	NullMask []bool
	Name     string
}

func (c *StringColumn) GetValue(index int) (interface{}, error) {
	if index < 0 || index >= len(c.Data) {
		return nil, fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return nil, nil
	}
	return c.Data[index], nil
}

func (c *StringColumn) GetStringValue(index int) (string, error) {
	if index < 0 || index >= len(c.Data) {
		return "", fmt.Errorf("index out of range")
	}
	if c.NullMask[index] {
		return "", nil
	}
	return c.Data[index], nil
}

func (c *StringColumn) IsNull(index int) bool {
	return index >= 0 && index < len(c.NullMask) && c.NullMask[index]
}

func (c *StringColumn) Length() int {
	return len(c.Data)
}

func (c *StringColumn) Type() string {
	return "string"
}

// Dataset holds the complete dataset with typed columns
type Dataset struct {
	IntColumns    map[string]*IntColumn
	FloatColumns  map[string]*FloatColumn
	StringColumns map[string]*StringColumn
	ColumnOrder   []string // Preserve original column order
	NumRows       int
}

// Loader handles loading CSV data into typed Dataset structures
type Loader struct {
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadFromCSV loads a CSV file into a Dataset using feature info for type conversion
func (l *Loader) LoadFromCSV(csvPath string, featureInfoMap map[string]*FeatureInfo) (*Dataset, error) {
	// 1. Parse raw CSV data using format layer
	rawData, err := l.csvParser.ParseFile(csvPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV: %w", err)
	}

	// 2. Validate structure
	if err := l.validateStructure(rawData, featureInfoMap); err != nil {
		return nil, fmt.Errorf("structure validation failed: %w", err)
	}

	// 3. Convert to typed dataset using efficient iterators
	dataset, err := l.convertToDataset(rawData, featureInfoMap)
	if err != nil {
		return nil, fmt.Errorf("conversion failed: %w", err)
	}

	return dataset, nil
}

// validateStructure ensures CSV structure matches feature info expectations
func (l *Loader) validateStructure(rawData *csv.ParsedData, featureInfoMap map[string]*FeatureInfo) error {
	// Check that all required features are present
	for featureName := range featureInfoMap {
		found := false
		for _, header := range rawData.Headers {
			if header == featureName {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("required feature '%s' not found in CSV headers", featureName)
		}
	}

	// Check for duplicate headers
	headerSet := make(map[string]bool)
	for _, header := range rawData.Headers {
		if headerSet[header] {
			return fmt.Errorf("duplicate header '%s' found", header)
		}
		headerSet[header] = true
	}

	return nil
}

// convertToDataset converts raw string data to typed columns using efficient iterators
func (l *Loader) convertToDataset(rawData *csv.ParsedData, featureInfoMap map[string]*FeatureInfo) (*Dataset, error) {
	dataset := &Dataset{
		IntColumns:    make(map[string]*IntColumn),
		FloatColumns:  make(map[string]*FloatColumn),
		StringColumns: make(map[string]*StringColumn),
		ColumnOrder:   rawData.Headers,
		NumRows:       rawData.NumRows,
	}

	// Convert each column based on its feature info using efficient iterators
	for _, header := range rawData.Headers {
		featureInfo, exists := featureInfoMap[header]
		if !exists {
			// Skip columns not in feature info (might be target or unused columns)
			continue
		}

		// Get column iterator (no data copying)
		columnIter, err := rawData.GetColumnIteratorByName(header)
		if err != nil {
			return nil, fmt.Errorf("failed to get column iterator for %s: %w", header, err)
		}

		// Convert based on HandleAs type using the iterator
		switch featureInfo.HandleAs {
		case HandleAsInteger:
			intCol, err := l.convertToIntColumnFromIterator(header, columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to integer: %w", header, err)
			}
			dataset.IntColumns[header] = intCol

		case HandleAsFloat:
			floatCol, err := l.convertToFloatColumnFromIterator(header, columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to float: %w", header, err)
			}
			dataset.FloatColumns[header] = floatCol

		case HandleAsString:
			stringCol, err := l.convertToStringColumnFromIterator(header, columnIter)
			if err != nil {
				return nil, fmt.Errorf("failed to convert column %s to string: %w", header, err)
			}
			dataset.StringColumns[header] = stringCol

		default:
			return nil, fmt.Errorf("unsupported handle_as type: %s for column %s", featureInfo.HandleAs, header)
		}
	}

	return dataset, nil
}

// convertToIntColumnFromIterator converts string data to integer column using iterator (no copying)
func (l *Loader) convertToIntColumnFromIterator(name string, iter *csv.ColumnIterator) (*IntColumn, error) {
	length := iter.Length()
	intData := make([]int64, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			continue
		}

		intVal, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("row %d: cannot convert '%s' to integer", i, value)
		}
		intData[i] = intVal
	}

	return &IntColumn{
		Data:     intData,
		NullMask: nullMask,
		Name:     name,
	}, nil
}

// convertToFloatColumnFromIterator converts string data to float column using iterator (no copying)
func (l *Loader) convertToFloatColumnFromIterator(name string, iter *csv.ColumnIterator) (*FloatColumn, error) {
	length := iter.Length()
	floatData := make([]float64, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			continue
		}

		floatVal, err := strconv.ParseFloat(value, 64)
		if err != nil {
			return nil, fmt.Errorf("row %d: cannot convert '%s' to float", i, value)
		}
		floatData[i] = floatVal
	}

	return &FloatColumn{
		Data:     floatData,
		NullMask: nullMask,
		Name:     name,
	}, nil
}

// convertToStringColumnFromIterator converts string data to string column using iterator (no copying)
func (l *Loader) convertToStringColumnFromIterator(name string, iter *csv.ColumnIterator) (*StringColumn, error) {
	length := iter.Length()
	stringData := make([]string, length)
	nullMask := make([]bool, length)

	for i := 0; i < length; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get value at row %d: %w", i, err)
		}

		if value == "" || value == "null" || value == "NULL" {
			nullMask[i] = true
			stringData[i] = ""
		} else {
			stringData[i] = value
		}
	}

	return &StringColumn{
		Data:     stringData,
		NullMask: nullMask,
		Name:     name,
	}, nil
}
