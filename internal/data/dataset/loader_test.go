package dataset

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLoaderIntegration(t *testing.T) {
	// Create a temporary CSV file
	csvContent := `name,age,salary,active
John,25,50000.5,true
<PERSON>,30,75000.0,false
<PERSON>,35,60000.25,true`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Create column specifications
	columnSpecs := []ColumnSpec{
		{Name: "name", Type: ColumnTypeString},
		{Name: "age", Type: ColumnTypeInteger},
		{Name: "salary", Type: ColumnTypeFloat},
		{Name: "active", Type: ColumnTypeString},
	}

	// Load dataset
	loader := NewLoader()
	dataset, err := loader.LoadFromCSV(csvFile, columnSpecs)
	if err != nil {
		t.Fatalf("LoadFromCSV failed: %v", err)
	}

	// Verify dataset structure
	if dataset.NumRows != 3 {
		t.Errorf("Expected 3 rows, got %d", dataset.NumRows)
	}

	// Test string column
	nameCol, exists := dataset.StringColumns["name"]
	if !exists {
		t.Fatal("Name column not found in StringColumns")
	}
	if nameCol.Length() != 3 {
		t.Errorf("Expected name column length 3, got %d", nameCol.Length())
	}

	nameValue, err := nameCol.GetStringValue(0)
	if err != nil {
		t.Fatalf("Failed to get name value: %v", err)
	}
	if nameValue != "John" {
		t.Errorf("Expected 'John', got '%s'", nameValue)
	}

	// Test integer column
	ageCol, exists := dataset.IntColumns["age"]
	if !exists {
		t.Fatal("Age column not found in IntColumns")
	}
	if ageCol.Length() != 3 {
		t.Errorf("Expected age column length 3, got %d", ageCol.Length())
	}

	ageValue, err := ageCol.GetValue(1)
	if err != nil {
		t.Fatalf("Failed to get age value: %v", err)
	}
	if ageValue != int64(30) {
		t.Errorf("Expected 30, got %v", ageValue)
	}

	// Test float column
	salaryCol, exists := dataset.FloatColumns["salary"]
	if !exists {
		t.Fatal("Salary column not found in FloatColumns")
	}
	if salaryCol.Length() != 3 {
		t.Errorf("Expected salary column length 3, got %d", salaryCol.Length())
	}

	salaryValue, err := salaryCol.GetValue(2)
	if err != nil {
		t.Fatalf("Failed to get salary value: %v", err)
	}
	if salaryValue != 60000.25 {
		t.Errorf("Expected 60000.25, got %v", salaryValue)
	}

	// Test active column (binary as string)
	activeCol, exists := dataset.StringColumns["active"]
	if !exists {
		t.Fatal("Active column not found in StringColumns")
	}

	activeValue, err := activeCol.GetStringValue(1)
	if err != nil {
		t.Fatalf("Failed to get active value: %v", err)
	}
	if activeValue != "false" {
		t.Errorf("Expected 'false', got '%s'", activeValue)
	}
}

func TestLoaderWithMissingValues(t *testing.T) {
	// Create CSV with missing values
	csvContent := `name,age,salary
John,25,50000.5
Jane,,75000.0
Bob,35,`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_missing.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	columnSpecs := []ColumnSpec{
		{Name: "name", Type: ColumnTypeString},
		{Name: "age", Type: ColumnTypeInteger},
		{Name: "salary", Type: ColumnTypeFloat},
	}

	loader := NewLoader()
	dataset, err := loader.LoadFromCSV(csvFile, columnSpecs)
	if err != nil {
		t.Fatalf("LoadFromCSV failed: %v", err)
	}

	// Test null handling in integer column
	ageCol := dataset.IntColumns["age"]
	if !ageCol.IsNull(1) {
		t.Error("Expected age at index 1 to be null")
	}

	// Test null handling in float column
	salaryCol := dataset.FloatColumns["salary"]
	if !salaryCol.IsNull(2) {
		t.Error("Expected salary at index 2 to be null")
	}

	// Test non-null values
	if ageCol.IsNull(0) {
		t.Error("Expected age at index 0 to not be null")
	}

	ageValue, err := ageCol.GetValue(0)
	if err != nil {
		t.Fatalf("Failed to get age value: %v", err)
	}
	if ageValue != int64(25) {
		t.Errorf("Expected 25, got %v", ageValue)
	}
}

func TestLoaderValidation(t *testing.T) {
	csvContent := `name,age
John,25
Jane,30`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_validation.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Test missing required column
	columnSpecs := []ColumnSpec{
		{Name: "name", Type: ColumnTypeString},
		{Name: "missing_column", Type: ColumnTypeInteger},
	}

	loader := NewLoader()
	_, err = loader.LoadFromCSV(csvFile, columnSpecs)
	if err == nil {
		t.Error("Expected error for missing required feature")
	}
}

func TestLoaderTypeConversionErrors(t *testing.T) {
	// Create CSV with invalid data for type conversion
	csvContent := `name,age
John,not_a_number
Jane,30`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_conversion.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	columnSpecs := []ColumnSpec{
		{Name: "age", Type: ColumnTypeInteger},
	}

	loader := NewLoader()
	_, err = loader.LoadFromCSV(csvFile, columnSpecs)
	if err == nil {
		t.Error("Expected error for invalid integer conversion")
	}
}

func TestLoaderAsStrings(t *testing.T) {
	// Create a CSV file with mixed data types
	csvContent := `name,age,salary,active
John,25,50000.5,true
Jane,30,75000.0,false`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_strings.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Load all columns as strings
	loader := NewLoader()
	dataset, err := loader.LoadFromCSVAsStrings(csvFile)
	if err != nil {
		t.Fatalf("LoadFromCSVAsStrings failed: %v", err)
	}

	// Verify all columns are strings
	if len(dataset.StringColumns) != 4 {
		t.Errorf("Expected 4 string columns, got %d", len(dataset.StringColumns))
	}

	if len(dataset.IntColumns) != 0 {
		t.Errorf("Expected 0 int columns, got %d", len(dataset.IntColumns))
	}

	if len(dataset.FloatColumns) != 0 {
		t.Errorf("Expected 0 float columns, got %d", len(dataset.FloatColumns))
	}

	// Test data access
	ageCol, exists := dataset.StringColumns["age"]
	if !exists {
		t.Fatal("Age column not found in StringColumns")
	}

	ageValue, err := ageCol.GetStringValue(0)
	if err != nil {
		t.Fatalf("Failed to get age value: %v", err)
	}
	if ageValue != "25" {
		t.Errorf("Expected '25', got '%s'", ageValue)
	}

	salaryValue, err := dataset.StringColumns["salary"].GetStringValue(1)
	if err != nil {
		t.Fatalf("Failed to get salary value: %v", err)
	}
	if salaryValue != "75000.0" {
		t.Errorf("Expected '75000.0', got '%s'", salaryValue)
	}
}
