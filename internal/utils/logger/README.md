# Logger Package

A simple structured logging system for Mulberri with operation-specific log files and automatic rotation.

## Features

- **Operation-Specific Log Files**: Separate log files for training and prediction
- **Date-Based Log Files**: Daily log files with automatic rotation
- **Automatic Log Management**: Size-based rotation, backup retention, and compression
- **Verbose Control**: Debug level logging controlled by CLI flags
- **Thread-Safe**: Concurrent access from multiple goroutines

## Simple Usage

Initialize the logger at the start of your program, then use standard logging functions throughout your code.

### Configuration-Based Approach
```go
// 1. First, set your values in getLogConfig() function in logger.go:
func getLogConfig() LogConfig {
    return LogConfig{
        // ... other defaults ...
        Verbose:   true,        // Set based on your CLI --verbose flag
        Operation: "train",     // Set based on your CLI operation
    }
}

// 2. Then use the logger anywhere in your code (auto-initializes):
import "github.com/berrijam/mulberri/internal/utils/logger"

func main() {
    // No initialization needed! Logger auto-initializes on first use

    // Use standard logging functions throughout your code
    logger.Info("Session started")
    logger.Debug("Debug info") // Shows only if Verbose=true in getLogConfig
    logger.Warn("Warning message")
    logger.Error("Error occurred")
}
```

## Log File Organization

The logger creates operation-specific log files in the `logs/` directory:

```
logs/
├── mulberri-train-2025-08-11.log      # Today's training logs
├── mulberri-train-2025-08-11.log.1.gz # Rotated training backup
├── mulberri-predict-2025-08-11.log    # Today's prediction logs
├── mulberri-predict-2025-08-11.log.1.gz # Rotated prediction backup
└── ...
```

**Rotation Settings:**
- **MaxSize**: 10MB per file (rotates when reached)
- **MaxBackups**: 7 backup files kept
- **MaxAge**: 7 days retention
- **Compression**: Backup files are compressed


## Available Logging Functions

Once you've initialized the logger, use these standard functions throughout your code:

```go
logger.Debug("Debug message")    // Only shows when verbose=true
logger.Info("Info message")
logger.Warn("Warning message")
logger.Error("Error message")
logger.Fatal("Fatal message")    // Exits the application
```

## CLI Integration

The logger is designed to work seamlessly with your CLI package:

```go
// In logger.go, modify getLogConfig() to use your CLI values:
func getLogConfig() LogConfig {
    return LogConfig{
        // ... other defaults ...
        Verbose:   cli.GetVerbose(),    // Get from your CLI package
        Operation: cli.GetOperation(), // Get from your CLI package
    }
}

// Then in your main application:
func main() {
    // No initialization needed! Logger auto-initializes on first use

    // Use standard logging functions throughout your code
    logger.Info("Application started")
    logger.Debug("This only shows when verbose=true")
}
```

## Log File Examples

### Training Log Output
```
2025-08-11 19:30:56| INFO |main.go:18 | Training session started
2025-08-11 19:30:56| DEBUG|main.go:19 | Loading dataset with 10,000 samples
2025-08-11 19:30:56| WARN |main.go:21 | Learning rate might be too high
2025-08-11 19:30:56| ERROR|main.go:23 | Failed to save checkpoint at epoch 1
```

### Prediction Log Output
```
2025-08-11 19:30:56| INFO |main.go:36 | Prediction session started
2025-08-11 19:30:56| WARN |main.go:39 | Low confidence for sample ID 123
2025-08-11 19:30:56| ERROR|main.go:41 | Failed to process sample ID 456
```

## Key Benefits

1. **Simple API**: Just call `InitForTrain()` or `InitForPredict()` then use standard logging functions
2. **Operation Separation**: Training and prediction logs are automatically separated into different files
3. **Date-Based Organization**: Logs are organized by date for easy historical analysis
4. **Automatic Management**: Old logs are automatically rotated and cleaned up
5. **Predictable Naming**: Log files follow consistent naming patterns
6. **Space Efficient**: Backup files are compressed to save disk space
7. **Verbose Control**: Debug messages only show when verbose flag is enabled
