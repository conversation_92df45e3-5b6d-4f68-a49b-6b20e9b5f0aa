package csv

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"
)

// ParsedData holds the raw CSV data in an efficient structure
// to avoid unnecessary copying of data throughout the system
type ParsedData struct {
	// Headers contains the column names from the first row (if hasHeader is true)
	Headers []string

	// Rows contains all data rows as string slices
	// Each row corresponds to one record from the CSV
	Rows [][]string

	// NumColumns is the number of columns in the dataset
	NumColumns int

	// NumRows is the number of data rows (excluding header if present)
	NumRows int
}

// ColumnIterator provides efficient column-wise access without copying data
type ColumnIterator struct {
	data     *ParsedData
	colIndex int
}

// GetColumnIterator returns an iterator for a specific column index
// This avoids copying data while providing column-wise access
func (pd *ParsedData) GetColumnIterator(colIndex int) (*ColumnIterator, error) {
	if colIndex < 0 || colIndex >= pd.NumColumns {
		return nil, fmt.Errorf("column index %d out of range [0, %d)", colIndex, pd.NumColumns)
	}

	return &ColumnIterator{
		data:     pd,
		colIndex: colIndex,
	}, nil
}

// GetValue returns the value at the specified row for this column
func (ci *ColumnIterator) GetValue(rowIndex int) (string, error) {
	if rowIndex < 0 || rowIndex >= ci.data.NumRows {
		return "", fmt.Errorf("row index %d out of range [0, %d)", rowIndex, ci.data.NumRows)
	}

	row := ci.data.Rows[rowIndex]
	if len(row) <= ci.colIndex {
		return "", fmt.Errorf("row %d has insufficient columns: expected %d, got %d", rowIndex, ci.data.NumColumns, len(row))
	}

	return row[ci.colIndex], nil
}

// Length returns the number of rows in this column
func (ci *ColumnIterator) Length() int {
	return ci.data.NumRows
}

// GetColumn returns all values for a specific column index (creates a copy)
// Use GetColumnIterator for more efficient access
func (pd *ParsedData) GetColumn(colIndex int) ([]string, error) {
	iter, err := pd.GetColumnIterator(colIndex)
	if err != nil {
		return nil, err
	}

	column := make([]string, pd.NumRows)
	for i := 0; i < pd.NumRows; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, err
		}
		column[i] = value
	}

	return column, nil
}

// GetColumnIteratorByName returns an iterator for a specific column name
func (pd *ParsedData) GetColumnIteratorByName(colName string) (*ColumnIterator, error) {
	if len(pd.Headers) == 0 {
		return nil, fmt.Errorf("no headers available")
	}

	colIndex := -1
	for i, header := range pd.Headers {
		if header == colName {
			colIndex = i
			break
		}
	}

	if colIndex == -1 {
		return nil, fmt.Errorf("column '%s' not found in headers", colName)
	}

	return pd.GetColumnIterator(colIndex)
}

// GetColumnByName returns all values for a specific column name (creates a copy)
// Use GetColumnIteratorByName for more efficient access
func (pd *ParsedData) GetColumnByName(colName string) ([]string, error) {
	iter, err := pd.GetColumnIteratorByName(colName)
	if err != nil {
		return nil, err
	}

	column := make([]string, pd.NumRows)
	for i := 0; i < pd.NumRows; i++ {
		value, err := iter.GetValue(i)
		if err != nil {
			return nil, err
		}
		column[i] = value
	}

	return column, nil
}

// GetRow returns a specific row by index
func (pd *ParsedData) GetRow(rowIndex int) ([]string, error) {
	if rowIndex < 0 || rowIndex >= pd.NumRows {
		return nil, fmt.Errorf("row index %d out of range [0, %d)", rowIndex, pd.NumRows)
	}

	return pd.Rows[rowIndex], nil
}

// GetValue returns a specific cell value by row and column index
func (pd *ParsedData) GetValue(rowIndex, colIndex int) (string, error) {
	if rowIndex < 0 || rowIndex >= pd.NumRows {
		return "", fmt.Errorf("row index %d out of range [0, %d)", rowIndex, pd.NumRows)
	}
	if colIndex < 0 || colIndex >= pd.NumColumns {
		return "", fmt.Errorf("column index %d out of range [0, %d)", colIndex, pd.NumColumns)
	}

	row := pd.Rows[rowIndex]
	if len(row) <= colIndex {
		return "", fmt.Errorf("row %d has insufficient columns: expected %d, got %d", rowIndex, pd.NumColumns, len(row))
	}

	return row[colIndex], nil
}

// Parser handles CSV file parsing with configurable options
type Parser struct {
	// Delimiter is the field delimiter (default: comma)
	Delimiter rune

	// HasHeader indicates if the first row contains column headers
	HasHeader bool

	// Comment character for ignoring lines (0 means no comments)
	Comment rune

	// TrimLeadingSpace removes leading whitespace from fields
	TrimLeadingSpace bool
}

// NewParser creates a new CSV parser with default settings
func NewParser() *Parser {
	return &Parser{
		Delimiter:        ',',
		HasHeader:        true,
		Comment:          0,
		TrimLeadingSpace: false,
	}
}

// ParseFile parses a CSV file and returns the structured data
func (p *Parser) ParseFile(filepath string) (*ParsedData, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", filepath, err)
	}
	defer file.Close()

	return p.Parse(file)
}

// Parse parses CSV data from an io.Reader and returns the structured data
func (p *Parser) Parse(reader io.Reader) (*ParsedData, error) {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = p.Delimiter
	csvReader.Comment = p.Comment
	csvReader.TrimLeadingSpace = p.TrimLeadingSpace

	// Read all records at once
	allRecords, err := csvReader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV: %w", err)
	}

	if len(allRecords) == 0 {
		return &ParsedData{
			Headers:    []string{},
			Rows:       [][]string{},
			NumColumns: 0,
			NumRows:    0,
		}, nil
	}

	var headers []string
	var dataRows [][]string
	numColumns := len(allRecords[0])

	if p.HasHeader {
		if len(allRecords) < 1 {
			return nil, fmt.Errorf("CSV has header flag set but no data rows")
		}
		headers = allRecords[0]
		dataRows = allRecords[1:]
	} else {
		headers = []string{} // No headers
		dataRows = allRecords
	}

	// Validate that all rows have the same number of columns
	for i, row := range dataRows {
		if len(row) != numColumns {
			return nil, fmt.Errorf("row %d has %d columns, expected %d", i, len(row), numColumns)
		}
	}

	return &ParsedData{
		Headers:    headers,
		Rows:       dataRows,
		NumColumns: numColumns,
		NumRows:    len(dataRows),
	}, nil
}
