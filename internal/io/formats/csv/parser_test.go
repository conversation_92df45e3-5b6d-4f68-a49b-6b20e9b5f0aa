package csv

import (
	"strings"
	"testing"
)

func TestParseWithHeader(t *testing.T) {
	csvData := `name,age,city
John,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	parser.HasHeader = true

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check headers
	expectedHeaders := []string{"name", "age", "city"}
	if len(data.Headers) != len(expectedHeaders) {
		t.Fatalf("Expected %d headers, got %d", len(expectedHeaders), len(data.Headers))
	}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.<PERSON><PERSON><PERSON>("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Check dimensions
	if data.NumColumns != 3 {
		t.<PERSON><PERSON>("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 rows, got %d", data.NumRows)
	}

	// Check data access
	nameColumn, err := data.GetColumnByName("name")
	if err != nil {
		t.Fatalf("GetColumnByName failed: %v", err)
	}
	expectedNames := []string{"John", "Jane", "Bob"}
	for i, expected := range expectedNames {
		if nameColumn[i] != expected {
			t.Errorf("Name %d: expected %s, got %s", i, expected, nameColumn[i])
		}
	}

	// Check specific value access
	value, err := data.GetValue(1, 2) // Jane's city
	if err != nil {
		t.Fatalf("GetValue failed: %v", err)
	}
	if value != "LA" {
		t.Errorf("Expected 'LA', got '%s'", value)
	}
}

func TestParseWithoutHeader(t *testing.T) {
	csvData := `John,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	parser.HasHeader = false

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check no headers
	if len(data.Headers) != 0 {
		t.Errorf("Expected no headers, got %d", len(data.Headers))
	}

	// Check dimensions
	if data.NumColumns != 3 {
		t.Errorf("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 3 {
		t.Errorf("Expected 3 rows, got %d", data.NumRows)
	}

	// Check column access by index
	firstColumn, err := data.GetColumn(0)
	if err != nil {
		t.Fatalf("GetColumn failed: %v", err)
	}
	expectedNames := []string{"John", "Jane", "Bob"}
	for i, expected := range expectedNames {
		if firstColumn[i] != expected {
			t.Errorf("Name %d: expected %s, got %s", i, expected, firstColumn[i])
		}
	}
}

func TestParseEmpty(t *testing.T) {
	csvData := ``

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}

func TestParseErrorCases(t *testing.T) {
	parser := NewParser()

	// Test column access out of bounds
	csvData := `a,b,c
1,2,3`
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	_, err = data.GetColumn(5)
	if err == nil {
		t.Error("Expected error for out of bounds column access")
	}

	_, err = data.GetValue(0, 5)
	if err == nil {
		t.Error("Expected error for out of bounds column access")
	}

	_, err = data.GetValue(5, 0)
	if err == nil {
		t.Error("Expected error for out of bounds row access")
	}

	// Test column by name when no headers
	parser.HasHeader = false
	data, err = parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	_, err = data.GetColumnByName("a")
	if err == nil {
		t.Error("Expected error when accessing column by name without headers")
	}
}

func TestCustomDelimiter(t *testing.T) {
	csvData := `name;age;city
John;25;NYC
Jane;30;LA`

	parser := NewParser()
	parser.Delimiter = ';'

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	if data.NumColumns != 3 {
		t.Errorf("Expected 3 columns, got %d", data.NumColumns)
	}

	value, err := data.GetValue(0, 1) // John's age
	if err != nil {
		t.Fatalf("GetValue failed: %v", err)
	}
	if value != "25" {
		t.Errorf("Expected '25', got '%s'", value)
	}
}

func TestColumnIterator(t *testing.T) {
	csvData := `name,age,city
John,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Test iterator by index
	ageIter, err := data.GetColumnIterator(1)
	if err != nil {
		t.Fatalf("GetColumnIterator failed: %v", err)
	}

	if ageIter.Length() != 3 {
		t.Errorf("Expected length 3, got %d", ageIter.Length())
	}

	// Test iterator values
	expectedAges := []string{"25", "30", "35"}
	for i, expected := range expectedAges {
		value, err := ageIter.GetValue(i)
		if err != nil {
			t.Fatalf("GetValue failed at index %d: %v", i, err)
		}
		if value != expected {
			t.Errorf("Age %d: expected %s, got %s", i, expected, value)
		}
	}

	// Test iterator by name
	nameIter, err := data.GetColumnIteratorByName("name")
	if err != nil {
		t.Fatalf("GetColumnIteratorByName failed: %v", err)
	}

	expectedNames := []string{"John", "Jane", "Bob"}
	for i, expected := range expectedNames {
		value, err := nameIter.GetValue(i)
		if err != nil {
			t.Fatalf("GetValue failed at index %d: %v", i, err)
		}
		if value != expected {
			t.Errorf("Name %d: expected %s, got %s", i, expected, value)
		}
	}
}

func TestIteratorErrorCases(t *testing.T) {
	csvData := `a,b,c
1,2,3`

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Test out of bounds column iterator
	_, err = data.GetColumnIterator(5)
	if err == nil {
		t.Error("Expected error for out of bounds column iterator")
	}

	// Test valid iterator with out of bounds row access
	iter, err := data.GetColumnIterator(0)
	if err != nil {
		t.Fatalf("GetColumnIterator failed: %v", err)
	}

	_, err = iter.GetValue(5)
	if err == nil {
		t.Error("Expected error for out of bounds row access")
	}

	// Test iterator by name when no headers
	parser.HasHeader = false
	data, err = parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	_, err = data.GetColumnIteratorByName("a")
	if err == nil {
		t.Error("Expected error when accessing column iterator by name without headers")
	}
}
